package ai.yourouter.organization.dailybill.service.impl;

import ai.yourouter.common.result.Result;
import ai.yourouter.common.utils.TimerUtils;
import ai.yourouter.jpa.organization.dailybill.repository.OrganizationDailyBillResponseRepository;
import ai.yourouter.jpa.organization.info.repository.OrganizationInfoRepository;
import ai.yourouter.organization.dailybill.service.OrganizationDailyBillService;
import ai.yourouter.organization.info.service.OrganizationInfoService;

import ai.yourouter.response.organization.OrganizationDailyBillResponse;
import lombok.RequiredArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationDailyBillServiceImpl implements OrganizationDailyBillService {

    private final OrganizationDailyBillResponseRepository organizationDailyBillResponseRepository;


    @Override
    public Result<Map<Long, List<OrganizationDailyBillResponse>>> showCharts(Integer selectedYear, Integer selectedMonth, String zoneId, Long organizationId) {
        ZoneId zone = ZoneId.of(zoneId);
        long[] range = TimerUtils.monthRange(selectedYear, selectedMonth, zone);

        List<OrganizationDailyBillResponse> list =
                organizationDailyBillResponseRepository.findBills(organizationId, range[0], range[1]);

        // 按 billDay 分组
        Map<Long, List<OrganizationDailyBillResponse>> groupedData = list.stream()
                .collect(Collectors.groupingBy(
                        OrganizationDailyBillResponse::getBillDay,
                        TreeMap::new,
                        Collectors.toList()));

        // 填充没有数据的日期，将所有值设为0
        fillMissingDays(groupedData, selectedYear, selectedMonth, zone, organizationId);

        return Result.success(groupedData);
    }


    /**
     * 填充当月没有数据的日期，所有值设为0
     *
     * @param groupedData    已分组的数据
     * @param year           年份
     * @param month          月份
     * @param zone           时区
     * @param organizationId 组织ID
     */
    private void fillMissingDays(Map<Long, List<OrganizationDailyBillResponse>> groupedData,
                                 Integer year, Integer month, ZoneId zone, Long organizationId) {
        LocalDate firstDay = LocalDate.of(year, month, 1);
        int daysInMonth = firstDay.lengthOfMonth();

        // 遍历当月所有日期
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate currentDate = LocalDate.of(year, month, day);
            long currentDayMillis = currentDate.atStartOfDay(zone).toInstant().toEpochMilli();

            // 如果当前日期不存在数据，则添加零值数据
            if (!groupedData.containsKey(currentDayMillis)) {
                List<OrganizationDailyBillResponse> emptyList = new ArrayList<>();

                // 添加LLM类型的空记录
                emptyList.add(createEmptyBillResponse(currentDayMillis, organizationId, "LLM", 0));

                // 添加Search类型的空记录
                emptyList.add(createEmptyBillResponse(currentDayMillis, organizationId, "Search", 1));

                groupedData.put(currentDayMillis, emptyList);
            }
        }
    }

    /**
     * 创建一个所有值为0的账单响应对象
     *
     * @param billDay        账单日期时间戳
     * @param organizationId 组织ID
     * @param modelName      模型名称
     * @param modelType      模型类型 (0=LLM, 1=Search)
     * @return 空的账单响应对象
     */
    private OrganizationDailyBillResponse createEmptyBillResponse(Long billDay, Long organizationId,
                                                                  String modelName, Integer modelType) {
        OrganizationDailyBillResponse response = new OrganizationDailyBillResponse();
        response.setBillDay(billDay);
        response.setOrganizationId(organizationId);
        response.setModelName(modelName);
        response.setModelType(modelType);

        // 设置所有数值为0
        response.setTextPrompt(0L);
        response.setTextCachePrompt(0L);
        response.setTextCompletion(0L);
        response.setTextCachePromptWrite(0L);
        response.setAudioPrompt(0L);
        response.setAudioCachePrompt(0L);
        response.setAudioCompletion(0L);
        response.setReasoningCompletion(0L);
        response.setImagePrompt(0L);
        response.setImageCachePrompt(0L);
        response.setImageCompletion(0L);
        response.setCall(0L);
        response.setRequest(0L);
        response.setQuota(BigDecimal.ZERO);

        return response;
    }

}
