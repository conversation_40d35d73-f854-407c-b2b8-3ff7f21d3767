package ai.yourouter.response.organization;

import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationDailyBillResponse {

    /** 当天 0 点毫秒时间戳 —— 作为 Map 的 key */
    private Long billDay;

    private Long organizationId;

    private String modelName;

    // ——— LLM 专属字段 ———
    private Long textPrompt;
    private Long textCachePrompt;
    private Long textCompletion;
    private Long textCachePromptWrite;
    private Long audioPrompt;
    private Long audioCachePrompt;
    private Long audioCompletion;
    private Long reasoningCompletion;
    private Long imagePrompt;
    private Long imageCachePrompt;
    private Long imageCompletion;

    // ——— Search & LLM 公共字段 ———
    private Long call;
    private Long request;

    /** 0 = LLM，1 = Search（可自行约定） */
    private Integer modelType;

    private BigDecimal quota;
}
